"""OneNetwork utilities."""

import time
from datetime import datetime
from typing import Optional, List

from selenium.common import StaleElementReferenceException
from selenium.webdriver.chrome.webdriver import WebDriver
from selenium.webdriver.common.by import By
from selenium.webdriver.remote.webelement import WebElement
from selenium.webdriver.support import expected_conditions as EC

from integrations.scheduling.models import AppointmentSlot
from integrations.scheduling.onenetwork.models import (
    ContactDetails,
    OneNetworkOpenSlotsResult,
    OneNetworkWarehouseDetails,
)
from utils.logging import logger
from utils.selenium import (
    get_element_text,
    is_element_present,
    safe_send_keys,
    wait_for_element,
)


def change_iframe(
    driver: WebDriver,
    iframe_locator: tuple,
    timeout: int = 10,
    index: int = False,
) -> None:
    """
    Change the iframe context of the driver.

    Args:
        driver: WebDriver instance
        iframe_locator: Locator of the iframe
        timeout: Timeout in seconds
        index: Index of the iframe if there are multiple iframes with the same locator
    """
    try:
        driver.switch_to.default_content()

        logger.info("Trying to close all perspectives first...")

        # Close all previous perspectives
        sidebar_buttons = driver.find_elements(
            By.CSS_SELECTOR, ".SidebarButton__container___oGMkc"
        )

        if len(sidebar_buttons) == 0:
            logger.warning("No sidebar buttons found")
            raise

        for button in sidebar_buttons:
            if "switch" in button.text.lower():
                if not button.is_enabled():
                    logger.warning("Switch button is disabled")
                    break
                button.click()
                break

        close_button = driver.find_element(
            By.CSS_SELECTOR,
            'button[aria-label="Close all previous perspectives"]',
        )

        if close_button.is_enabled():
            close_button.click()
            logger.info("Closed all previous perspectives")
        else:
            logger.info("Close button is disabled — no perspectives to close")
    except Exception:
        logger.warning(
            "Failed to close previous perspectives: button not interactable..."
        )

    logger.info("Waiting for iframe to load...")
    iframe = wait_for_element_with_polling(
        driver,
        iframe_locator,
        timeout=10,
        poll_interval=0.3,
    )

    if not index:
        if not iframe:
            raise Exception("Required Iframe not found")
        driver.switch_to.frame(iframe)
    else:
        iframes = driver.find_elements(*iframe_locator)
        if not iframes:
            raise Exception("Required Iframe not found")
        driver.switch_to.frame(iframes[index])

    loading_indicator_wait(driver)


def wait_for_element_with_polling(
    driver: WebDriver,
    locator: tuple,
    timeout: int = 10,
    poll_interval: float = 0.5,
) -> Optional[WebElement]:
    """
    Wait for an element to appear in the DOM with polling.
    Args:
        driver: WebDriver instance
        locator: Element locator (By, value)
        timeout: Maximum time to wait in seconds
        poll_interval: Time to wait between polls in seconds
    Returns:
        WebElement if found, None if not found within timeout
    """
    driver.implicitly_wait(0)

    max_attempts = int(timeout / poll_interval)

    for attempt in range(max_attempts):
        try:
            name_element = driver.find_element(*locator)
            if name_element.is_displayed():
                logger.info(
                    f"Element appeared after {attempt * poll_interval:.1f} seconds."
                )
                driver.implicitly_wait(10)
                return name_element
        except Exception:
            # Element might be gone from DOM — also counts as disappeared
            # logger.info("Element not present in DOM, rechecking...")
            pass

        time.sleep(poll_interval)
    else:
        logger.error("Element did not appear within timeout.")

    driver.implicitly_wait(10)

    return None


def wait_for_elements_with_polling(
    driver: WebDriver,
    locator: tuple,
    timeout: int = 10,
    poll_interval: float = 0.5,
) -> List[WebElement]:
    """
    Wait for an element to appear in the DOM with polling.
    Args:
        driver: WebDriver instance
        locator: Element locator (By, value)
        timeout: Maximum time to wait in seconds
        poll_interval: Time to wait between polls in seconds
    Returns:
        A list of WebElements if found, empty list [] if not found within timeout
    """
    driver.implicitly_wait(0)

    max_attempts = int(timeout / poll_interval)

    for attempt in range(max_attempts):
        try:
            name_element = driver.find_elements(*locator)
            if name_element and all(el.is_displayed() for el in name_element):
                logger.info(
                    f"Elements appeared after {attempt * poll_interval:.1f} seconds."
                )
                driver.implicitly_wait(10)
                return name_element

            logger.info("Elements not yet displayed, rechecking...")
        except Exception:
            # Element might be gone from DOM — also counts as disappeared
            # logger.info("Element not present in DOM, rechecking...")
            pass

        time.sleep(poll_interval)
    else:
        logger.error("Element did not appear within timeout.")

    driver.implicitly_wait(10)

    return []


def extract_warehouse_details(
    driver: WebDriver, available_slots: List[AppointmentSlot] = None
):
    """Extract warehouse details from the OneNetwork UI.

    Args:
        driver: WebDriver instance
        available_slots: List of available appointment slots

    Returns:
        OneNetworkWarehouseDetails object or None if details couldn't be extracted
    """
    locators = {
        "name": (By.CSS_SELECTOR, "div[name='Name'].x-form-display-field"),
        "timezone": (
            By.CSS_SELECTOR,
            "div[name='TimeZoneId'].x-form-display-field",
        ),
        "address": (
            By.CSS_SELECTOR,
            "div[name='Address'].x-form-display-field",
        ),
    }

    logger.info("Waiting for warehouse details to load...")

    name_element = wait_for_element_with_polling(
        driver=driver,
        locator=locators["name"],
        timeout=10,
    )

    try:
        # Site details not available
        if not name_element:
            logger.warning("Warehouse details not found")
            return None

        # Extract basic details
        warehouse_name = name_element.get_attribute("title").strip()
        timezone = (
            driver.find_element(*locators["timezone"])
            .get_attribute("title")
            .strip()
        )

        # Parse address
        address_text = driver.find_element(*locators["address"]).text.strip()
        address_parts = [
            part.strip() for part in address_text.split("\n") if part.strip()
        ]

        # Extract location details using index-based access
        city, state, zip_code, country = "", "", "", ""

        # Country is typically the last element
        if address_parts and len(address_parts) > 0:
            country = address_parts[-1]

        # City, state, zip is typically the second-to-last element
        if len(address_parts) > 1:
            location_line = address_parts[-2]
            if "," in location_line:
                try:
                    city = location_line.split(",")[0].strip()
                    state_zip = location_line.split(",")[1].strip().split()
                    state = state_zip[0] if state_zip else ""
                    zip_code = state_zip[1] if len(state_zip) > 1 else ""
                except Exception as e:
                    logger.warning(f"Error parsing location: {str(e)}")

        # Create and return warehouse details
        warehouse_details = OneNetworkWarehouseDetails(
            name=warehouse_name,
            city=city,
            state=state,
            country=country,
            zipCode=zip_code,
            timezone=timezone,
            openSlots=available_slots,
            stopType="dropoff",
        )

        logger.info(
            f"Extracted warehouse: {warehouse_name} ({city}, {state}, {country})"
        )
        return warehouse_details

    except Exception as e:
        logger.error(f"Error extracting warehouse details: {str(e)}")
        return None


def switch_to_site(driver: WebDriver) -> bool:
    """Fetch warehouse details from the OneNetwork UI.

    Args:
        driver: WebDriver instance

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        site_name_locator = (
            By.CSS_SELECTOR,
            "span.AutocompleteField__selectedItem___xiloR.AutocompleteField__clickable____fypE, "
            + "span.AutocompleteField__selectedItem___xiloR.AutocompleteField__disabled___rLHKc.AutocompleteField__clickable____fypE",
        )

        site_name = driver.find_element(*site_name_locator)
        site_name.click()

        logger.info(f"Clicked on site name: {site_name.text}")

        loading_indicator_wait(driver)

        iframe_locator3 = (
            By.CSS_SELECTOR,
            "iframe.Perspective__frame___iATIU",
        )
        change_iframe(driver, iframe_locator3, index=-1)

        return True
    except Exception as e:
        logger.error(f"Error switching to site: {str(e)}")
        return False


def access_global_search(
    driver: WebDriver, ship_no: str = None, ref_no: str = None
) -> bool:
    """Access the global search functionality in the OneNetwork UI.

    Args:
        driver: WebDriver instance
        ship_no: Ship number to search for
        ref_no: Reference number to search for

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Validate at least one search parameter is provided
        if not ship_no and not ref_no:
            logger.error("Either ship_no or ref_no must be provided")
            return False

        global_button_locator = (
            By.CSS_SELECTOR,
            ".SidebarButton__container___oGMkc.GlobalSidebar__button___UxNzn svg.fa-search",
        )

        search_input_locator = (
            By.CSS_SELECTOR,
            "input.StringField__textField___JEkir[name='searchText']",
        )

        global_search_button = wait_for_element(
            driver,
            global_button_locator,
            condition=EC.element_to_be_clickable,
        )

        if not global_search_button:
            logger.error("Global search button not found")
            return False

        slideout_locator = (By.CLASS_NAME, "SearchSlideout__content___F2thB")
        max_attempts = 15

        for attempt in range(1, max_attempts + 1):
            try:
                logger.info(
                    f"Attempt {attempt}: Clicking global search button"
                )
                global_search_button.click()
            except StaleElementReferenceException:
                logger.warning(
                    "Global search button became stale, re-locating..."
                )
                global_search_button = wait_for_element(
                    driver,
                    global_button_locator,
                    condition=EC.element_to_be_clickable,
                )
                if not global_search_button:
                    logger.error(
                        "Global search button not found after becoming stale"
                    )
                    return False
                global_search_button.click()

            time.sleep(0.5)

            try:
                slideout = driver.find_element(*slideout_locator)
                if slideout.is_displayed():
                    logger.info(
                        f"Search slideout became visible on attempt {attempt}"
                    )
                    break
            except Exception:
                logger.debug("Slideout element not yet visible")

            logger.warning(
                f"Slideout not visible, retrying... ({attempt}/{max_attempts})"
            )

        else:
            logger.error(
                "Search slideout did not appear after multiple clicks"
            )
            return False

        if ref_no:
            ref_no = "*00" + ref_no
            logger.info(f"Searching by reference number: {ref_no}")

            dropdown_locator = (
                By.CSS_SELECTOR,
                ".ComboField__trigger___B61kB",
            )
            dropdown = wait_for_element_with_polling(
                driver, dropdown_locator, timeout=10
            )
            if not dropdown:
                logger.error("Search type dropdown not found")
                return False

            dropdown.click()

            options_locator = (
                By.XPATH,
                "//div[contains(@class, 'DropdownList__listItem___XEbrj') and contains(@class, 'DropdownList__selectable___L1RDk')]",
            )

            ref_no_option = None
            for opt in driver.find_elements(*options_locator):
                if "Shipments - by Order Ref No" in opt.text:
                    ref_no_option = opt
                    break

            if not ref_no_option:
                logger.error("Shipments - by Order Ref No option not found")
                return False

            ref_no_option.click()
        else:
            ship_no = "*" + ship_no
            logger.info(f"Searching by ship number: {ship_no}")

        driver.implicitly_wait(2)

        try:
            search_input = wait_for_element(
                driver,
                search_input_locator,
                condition=EC.visibility_of_element_located,
            )
            if not search_input:
                raise Exception("Search input not found")
        except Exception as e:
            logger.warning(f"Search input not found, retrying: {str(e)}")
            time.sleep(1)

            search_input = wait_for_element(
                driver,
                search_input_locator,
                timeout=10,
                condition=EC.visibility_of_element_located,
            )
            if not search_input:
                logger.error("Search input not found after retry")
                driver.implicitly_wait(10)
                return False

        driver.implicitly_wait(10)

        if not inout_and_click_search_button(
            driver, search_input, ship_no or ref_no
        ):
            logger.error("Failed to click search button")
            return False

        iframe_locator = (
            By.CSS_SELECTOR,
            "iframe.Perspective__legacy___iWM3V",
        )

        change_iframe(driver, iframe_locator)

        logger.info("Global search completed successfully")
        return True

    except Exception as e:
        logger.error(f"Error in access_global_search: {str(e)}")
        return False


def inout_and_click_search_button(
    driver: WebDriver,
    search_input: WebElement,
    value: str,
    max_attempts: int = 3,
) -> bool:
    """Click the search button in the OneNetwork UI.

    Args:
        driver: WebDriver instance
        search_input: Search input WebElement
        value: Value to input into the search field
        max_attempts: Maximum number of attempts to click the search button

    Returns:
        bool: True if successful, False otherwise
    """
    search_button_locator = (
        By.CSS_SELECTOR,
        ".Button__primary___FSeua.SearchSlideout__searchButton___g1OyQ",
    )
    iframe_locator = (
        By.CSS_SELECTOR,
        "iframe.Perspective__legacy___iWM3V",
    )

    driver.implicitly_wait(2)

    click_success = False
    for attempt in range(max_attempts):
        try:
            logger.info(
                f"Attempting to input and click search (attempt {attempt+1}/{max_attempts})"
            )
            search_input.clear()
            search_input.send_keys(value)

            # Wait for button to be clickable
            search_button = wait_for_element(
                driver,
                search_button_locator,
                condition=EC.element_to_be_clickable,
            )
            if not search_button:
                logger.warning("Search button not found or not clickable")
                continue

            time.sleep(0.5)
            search_button.click()
            logger.info("Search button clicked successfully")
            click_success = True
            break
        except Exception as e:
            logger.warning(f"Error during input/click: {str(e)}. Retrying...")

    if not click_success:
        logger.error(
            f"Failed to click search button after {max_attempts} attempts"
        )
        driver.implicitly_wait(10)
        return False

    # Now poll for iframe (longer timeout, as loading can take time)
    if wait_for_element_with_polling(
        driver,
        iframe_locator,
        timeout=15,
        poll_interval=0.2,  # Tighter polling
    ):
        logger.info("Search completed successfully")
        driver.implicitly_wait(10)
        return True

    logger.error("Iframe did not appear after click")
    driver.implicitly_wait(10)
    return False


def set_target_date(driver: WebDriver, target_date: str) -> bool:
    """Set the target date in the OneNetwork UI.

    Args:
        driver: WebDriver instance
        target_date: Date string in format YYYY-MM-DD

    Returns:
        bool: True if successful, False otherwise
    """
    date_picker_locator = (
        By.CSS_SELECTOR,
        ".DateTimeField__input___Os_0_",
    )

    try:
        loading_indicator_wait(driver)

        logger.info(f"Setting target date to {target_date}")
        # Wait for date picker to be visible
        date_picker = wait_for_element_with_polling(
            driver,
            date_picker_locator,
            timeout=10,
        )
        if not date_picker:
            logger.warning("Date picker not found")
            return False

        driver.execute_script(
            """
            const input = arguments[0];
            const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, "value").set;
            nativeInputValueSetter.call(input, arguments[1]);
            // Dispatch both input and change to trigger React updates
            input.dispatchEvent(new Event('change', { bubbles: true }));
            input.dispatchEvent(new Event('input', { bubbles: true }));
        """,
            date_picker,
            target_date,
        )

        loading_indicator_wait(driver)

        # time.sleep(0.2)

        return True
    except Exception as e:
        logger.error(f"Error setting target date: {str(e)}")
        return False


def switch_to_shipment_details(driver: WebDriver) -> bool:
    """Switch to the shipment details iframe in the OneNetwork UI.

    Args:
        driver: WebDriver instance

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        logger.info("Switching to shipment details...")
        filter_toolbar_locator = (By.CSS_SELECTOR, ".filter-plugin-toolbar")
        wait_for_element_with_polling(
            driver, filter_toolbar_locator, timeout=10
        )

        grid_locator = (By.CSS_SELECTOR, ".x-grid3-body")
        wait_for_element_with_polling(driver, grid_locator, timeout=10)

        appointment_link_locator = (By.CSS_SELECTOR, ".appointment-link")
        appointment_links = wait_for_elements_with_polling(
            driver, appointment_link_locator, timeout=10
        )

        if appointment_links:
            logger.info(f"Found {len(appointment_links)} appointment links")

            first_link = appointment_links[0]
            link_text = first_link.text
            logger.info(f"Clicking on appointment link: {link_text}")
            first_link.click()

            iframe_locator2 = (
                By.CSS_SELECTOR,
                "iframe.Perspective__frame___iATIU",
            )
            change_iframe(driver, iframe_locator2, index=-1)

            loading_indicator_wait(driver)

            return True
        else:
            logger.warning("No appointment links found in search results")
            return False
    except Exception as e:
        logger.error(f"Error switching to shipment details: {str(e)}")
        return False


def loading_indicator_wait(driver: WebDriver) -> None:
    """Wait for the loading indicator to disappear.

    Args:
        driver: WebDriver instance
    """
    loading_indicator_locator = (
        By.CSS_SELECTOR,
        ".LoadingMask__maskTextCt___GVl13:last-child, .LoadingMask__maskTextCt___GVl13:nth-last-child(1)",
    )
    logger.info("Waiting for loading indicator to disappear...")

    timeout = 15
    poll_frequency = 0.2
    max_attempts = int(timeout / poll_frequency)

    driver.implicitly_wait(0)

    for attempt in range(max_attempts):
        try:
            element = driver.find_element(*loading_indicator_locator)
            if not element.is_displayed():
                logger.info(
                    f"Loading indicator disappeared after {attempt * poll_frequency:.1f} seconds."
                )
                break
        except Exception:
            logger.info("Loading indicator element no longer present in DOM.")
            break
        time.sleep(poll_frequency)
    else:
        logger.error("Loading indicator did not disappear within timeout.")

    driver.implicitly_wait(10)


def fetch_open_slots(
    driver: WebDriver,
    target_date: datetime,
    formatted_date: str,
    match_row: bool = False,
) -> OneNetworkOpenSlotsResult:
    """Fetch open slots for the given date range.

    Args:
        driver: WebDriver instance
        target_date: Target date for slot search
        formatted_date: Formatted date string
        match_row: Whether to match the open slot date with the target date or not

    Returns:
        OneNetworkOpenSlotsResult with open slots
    """
    try:
        available_slots = []

        logger.info(f"Fetching open slots for date: {formatted_date}")

        if not set_target_date(driver, formatted_date):
            logger.error(f"Failed to set start date to {formatted_date}")
            return OneNetworkOpenSlotsResult(
                success=False, message="Failed to set start date"
            )

        slots_table_locator = (
            By.CSS_SELECTOR,
            "div[data-instance^='tabulator-']",
        )
        row_xpath_locator = (
            By.XPATH,
            "//div[contains(@class, 'tabulator-row')][.//div[contains(text(), 'Regular')]]",
        )

        wait_for_element_with_polling(driver, slots_table_locator, timeout=10)
        rows = driver.find_elements(*row_xpath_locator)

        if rows:
            logger.info(f"Found {len(rows)} 'Regular' slot rows to process.")
            for row in rows:
                slot_texts = row.text.strip().split("\n")
                if len(slot_texts) < 2:
                    continue

                slot_type_text = slot_texts[1].strip()
                start_time_text = slot_texts[0].strip()

                if "regular" not in slot_type_text.lower():
                    logger.info(f"Skipping non-regular slot: {slot_type_text}")
                    continue

                try:
                    dt_obj = datetime.strptime(
                        start_time_text, "%b %d, %Y %I:%M %p"
                    )
                    iso_datetime = dt_obj.strftime("%Y-%m-%dT%H:%M:%S")
                    slot = AppointmentSlot(
                        scheduledTime=iso_datetime,
                        duration=60,
                    )

                    if match_row:
                        if start_time_text == formatted_date:
                            try:
                                radio_button = row.find_element(
                                    By.CSS_SELECTOR, '[role="radio"]'
                                )
                                radio_button.click()
                                logger.info(
                                    f"Clicked radio button for exact match: {start_time_text}"
                                )
                                available_slots.append(slot)
                                break
                            except Exception as e:
                                logger.error(
                                    f"Failed to click radio button: {e}"
                                )
                        else:
                            continue
                    else:
                        available_slots.append(slot)

                except Exception as e:
                    logger.error(
                        f"Error parsing slot time '{start_time_text}': {str(e)}"
                    )

        if not available_slots:
            logger.info(f"No available slots found for date {formatted_date}")
            return OneNetworkOpenSlotsResult(
                success=False, message="No available slots found"
            )

        return OneNetworkOpenSlotsResult(
            success=True,
            message="Successfully retrieved open slots",
            openSlots=available_slots,
        )
    except Exception as e:
        logger.error(f"Error fetching open slots: {str(e)}")
        return OneNetworkOpenSlotsResult(
            success=False,
            message="Failed to fetch open slots",
            errors=[str(e)],
        )


def input_contact_details(
    driver: WebDriver, contact_details: ContactDetails
) -> bool:
    """Input contact details for the appointment.

    Args:
        driver: WebDriver instance
        contact_details: Contact details to input

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        edit_button = driver.find_element(
            By.XPATH, "//button[span[text()='Edit']]"
        )
        edit_button.click()

        # Find the contact details input fields
        phone_input_locator = (
            By.CSS_SELECTOR,
            "input[name='phone']",
        )
        email_input_locator = (
            By.CSS_SELECTOR,
            "input[name='contact']",
        )

        phone_input = wait_for_element_with_polling(
            driver, phone_input_locator, timeout=10
        )
        email_input = wait_for_element_with_polling(
            driver, email_input_locator, timeout=10
        )

        if not phone_input or not email_input:
            logger.error("Failed to find contact details input fields")
            return False

        # Input the contact details
        safe_send_keys(driver, phone_input, contact_details.phone)
        safe_send_keys(driver, email_input, contact_details.email)

        ok_button = driver.find_element(
            By.XPATH, "//button[span[text()='OK']]"
        )
        ok_button.click()

        return True
    except Exception as e:
        logger.error(f"Error inputting contact details: {str(e)}")
        return False
