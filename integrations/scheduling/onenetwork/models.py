"""OneNetwork-specific data models."""

from typing import Optional, List
from datetime import datetime, timedelta

from pydantic import BaseModel, model_validator, field_validator

from integrations.scheduling.models import (
    SchedulingBaseRequest,
    SchedulingActionType,
    SchedulingPlatform,
    Credentials,
    WarehouseDetails,
    AppointmentSlot,
)
from models.base import BaseResponse


class OneNetworkWarehouseDetails(WarehouseDetails):
    """OneNetwork-specific warehouse details."""

    timezone: Optional[str] = ""


class ContactDetails(BaseModel):
    """Contact details for OneNetwork appointments."""

    phone: str = ""
    email: str = ""


class OneNetworkAppointmentData(BaseModel):
    """OneNetwork appointment data."""

    refNo: Optional[str] = ""
    shipNo: Optional[List[str]] = ""
    scheduledTime: str
    duration: int
    notes: Optional[str] = ""
    status: Optional[str] = ""

    contact: ContactDetails = None
    warehouse: Optional[WarehouseDetails] = None

    @model_validator(mode="after")
    def validate_reference_numbers(self):
        """Validate that at least one of shipNo or refNo is provided."""
        if not self.shipNo and not self.refNo:
            raise ValueError(
                "At least one of shipNo or refNo must be provided"
            )
        if self.shipNo and self.refNo:
            raise ValueError("Only one of shipNo or refNo must be provided")
        return self

    @field_validator("scheduledTime")
    def validate_appointment_time(cls, v):
        """Validate appointment time format."""
        try:
            datetime.strptime(v, "%Y-%m-%dT%H:%M:%S.%fZ")
            return v
        except ValueError:
            raise ValueError(
                "Invalid appointment time format. Expected: YYYY-MM-DDTHH:mm:ss.sssZ"
            )


class OneNetworkGetOpenSlotsAppointmentData(BaseModel):
    """OneNetwork open slots appointment data."""

    notes: Optional[str] = ""
    status: Optional[str] = ""
    warehouse: Optional[OneNetworkWarehouseDetails] = None


class OneNetworkLoginRequest(SchedulingBaseRequest):
    """OneNetwork login request model."""

    action: SchedulingActionType = SchedulingActionType.LOGIN
    platform: SchedulingPlatform = SchedulingPlatform.ONENETWORK

    credentials: Credentials


class OneNetworkGetLoadTypesRequest(SchedulingBaseRequest):
    """OneNetwork-specific GET load types request."""

    action: SchedulingActionType = SchedulingActionType.GET_LOAD_TYPES
    platform: SchedulingPlatform = SchedulingPlatform.ONENETWORK

    endDate: str
    filterType: Optional[str] = ""
    locationId: Optional[str] = ""
    startDate: str


class OneNetworkGetOpenSlotsRequest(SchedulingBaseRequest):
    """OneNetwork-specific GET open slots request."""

    action: SchedulingActionType = SchedulingActionType.GET_OPEN_SLOTS
    platform: SchedulingPlatform = SchedulingPlatform.ONENETWORK

    endDate: Optional[str] = None
    filterType: Optional[str] = ""
    locationId: Optional[str] = ""
    startDate: Optional[str] = None
    shipNo: Optional[List[str]] = None
    refNo: Optional[str] = ""

    @model_validator(mode="after")
    def validate_reference_numbers(self):
        """Validate that at least one of shipNo or refNo is provided."""
        if not self.shipNo and not self.refNo:
            raise ValueError(
                "At least one of shipNo or refNo must be provided"
            )
        if self.shipNo and self.refNo:
            raise ValueError("Only one of shipNo or refNo must be provided")
        return self

    @model_validator(mode="after")
    def validate_dates(self):
        """Validate date fields and set defaults if not provided."""
        if not self.startDate and not self.endDate:
            today = datetime.now()
            one_week_later = today + timedelta(days=7)

            self.startDate = today.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
            self.endDate = one_week_later.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
            return self

        if bool(self.startDate) != bool(self.endDate):
            raise ValueError(
                "Both startDate and endDate must be provided together"
            )

        try:
            start_date = datetime.strptime(
                self.startDate, "%Y-%m-%dT%H:%M:%S.%fZ"
            )
            end_date = datetime.strptime(self.endDate, "%Y-%m-%dT%H:%M:%S.%fZ")

            if end_date < start_date:
                raise ValueError("End date must be on or after start date")

        except ValueError as e:
            if "does not match format" in str(
                e
            ) or "unconverted data remains" in str(e):
                raise ValueError(
                    "Date format must be YYYY-MM-DDTHH:mm:ss.sssZ"
                )
            raise

        return self


class OneNetworkGetOpenSlotsResponse(BaseResponse):
    """OneNetwork-specific response containing open appointment slots."""

    appointments: Optional[List[OneNetworkGetOpenSlotsAppointmentData]] = None


class OneNetworkGetWarehouseRequest(SchedulingBaseRequest):
    """OneNetwork-specific GET warehouse request."""

    action: SchedulingActionType = SchedulingActionType.GET_WAREHOUSE
    platform: SchedulingPlatform = SchedulingPlatform.ONENETWORK

    endDate: str
    filterType: Optional[str] = ""
    locationId: Optional[str] = ""
    startDate: str


class OneNetworkCancelAppointmentRequest(SchedulingBaseRequest):
    """OneNetwork-specific cancel appointment request."""

    action: SchedulingActionType = SchedulingActionType.CANCEL_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.ONENETWORK

    reason: Optional[str] = ""
    shipNo: Optional[str] = ""
    refNo: Optional[str] = ""

    @model_validator(mode="after")
    def validate_reference_numbers(self):
        """Validate that at least one of shipNo or refNo is provided."""
        if not self.shipNo and not self.refNo:
            raise ValueError(
                "At least one of shipNo or refNo must be provided"
            )
        if self.shipNo and self.refNo:
            raise ValueError("Only one of shipNo or refNo must be provided")
        return self

    @model_validator(mode="after")
    def validate_reason_length(self):
        """Validate that reason is not longer than 64 characters."""
        if self.reason and len(self.reason) > 64:
            raise ValueError("Reason cannot be longer than 64 characters")
        return self


class OneNetworkGetAppointmentRequest(SchedulingBaseRequest):
    """OneNetwork-specific get appointment request."""

    action: SchedulingActionType = SchedulingActionType.GET_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.ONENETWORK

    shipNo: Optional[str] = ""
    refNo: Optional[str] = ""

    @model_validator(mode="after")
    def validate_reference_numbers(self):
        """Validate that at least one of shipNo or refNo is provided."""
        if not self.shipNo and not self.refNo:
            raise ValueError(
                "At least one of shipNo or refNo must be provided"
            )
        if self.shipNo and self.refNo:
            raise ValueError("Only one of shipNo or refNo must be provided")
        return self


class OneNetworkMakeAppointmentRequest(SchedulingBaseRequest):
    """OneNetwork-specific make appointment request."""

    action: SchedulingActionType = SchedulingActionType.MAKE_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.ONENETWORK

    appointments: List[OneNetworkAppointmentData]


class OneNetworkMakeAppointmentResponse(BaseResponse):
    """Response after creating an appointment."""

    appointments: Optional[List[OneNetworkAppointmentData]] = None


class OneNetworkUpdateAppointmentRequest(SchedulingBaseRequest):
    """OneNetwork-specific update appointment request."""

    action: SchedulingActionType = SchedulingActionType.UPDATE_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.ONENETWORK

    appointments: List[OneNetworkAppointmentData]


class OneNetworkUpdateAppointmentResponse(BaseResponse):
    """Response after creating an appointment."""

    appointments: Optional[List[OneNetworkAppointmentData]] = None


class OneNetworkOpenSlotsResult(BaseModel):
    """OneNetwork-specific open slots result."""

    success: bool
    message: Optional[str] = ""
    errors: Optional[List[str]] = None
    openSlots: Optional[List[AppointmentSlot]] = None
