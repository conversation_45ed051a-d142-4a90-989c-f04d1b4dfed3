"""YardView-specific data models."""

from typing import Any, Dict, List, Optional
from enum import Enum

from pydantic import BaseModel, validator

from models.base import RequestMode
from integrations.scheduling.models import (
    Appointment,
    BaseResponse,
    Credentials,
    GetWarehouseResponse,
    SchedulingActionType,
    SchedulingBaseRequest,
    SchedulingPlatform,
    WarehouseDetails,
)


######################################################################
# API
######################################################################


class RequestSource(str, Enum):
    """Request source for YardView."""

    BASE = "https://targettest.yardview.com/api"
    ELWOOD = "https://target.yardview.com/api"
    ALLENTOWN = "https://allentown.yardview.com/api"


class YardViewSchedulingBaseRequest(SchedulingBaseRequest):
    """Base request model for YardView integration."""

    source: Optional[RequestSource] = RequestSource.BASE

    @validator("source", pre=True)
    def validate_and_transform_source(cls, value):
        if isinstance(value, str):
            if value == "1":
                return RequestSource.ELWOOD
            elif value == "2":
                return RequestSource.ALLENTOWN
            else:
                return RequestSource.BASE
        return value


class Endpoints:
    """API endpoint paths for YardView."""

    LOAD_TYPES = "/loadtypes"
    LOCATIONS = "/locations"
    SCHEDULE_DETAILS = "/schedule/details"
    APPOINTMENTS = "/appointments"
    APPOINTMENT_BY_ID = "/appointments/pid/{appointmentId}"
    APPOINTMENT_CANCEL = "/appointments/{appointmentId}/cancel"
    SCACS = "/scacs"


class AppointmentStatus(Enum):
    PENDING = 0
    CANCELLED = 1
    COMPLETED = 2

    @classmethod
    def from_string(cls, value: str | None) -> Optional[int]:
        if value is None:
            return None

        status_map = {
            "pending": cls.PENDING.value,
            "cancelled": cls.CANCELLED.value,
            "completed": cls.COMPLETED.value,
        }

        try:
            return status_map[value.lower()]
        except (KeyError, AttributeError):
            raise ValueError(
                "Invalid status. Valid values are: 'pending', 'cancelled', 'completed'"
            )


class AppointmentType(Enum):
    PICKUP = "Pickup"
    DELIVERY = "Delivery"

    @classmethod
    def from_string(cls, value: str | None) -> Optional[str]:
        if value is None:
            return None

        if value in [cls.PICKUP.value, cls.DELIVERY.value]:
            return value

        appointment_type_map = {
            "dropoff": cls.DELIVERY.value,
            "pickup": cls.PICKUP.value,
        }

        try:
            return appointment_type_map[value.lower()]
        except (KeyError, AttributeError):
            raise ValueError(
                "Invalid appointment type. Valid values are: 'dropoff' or 'pickup'"
            )


class ScheduleTypes(Enum):
    INBOUND = "Inbound"
    TARGET_AIR_FREIGHT_ONLY = "TargetAirFreightOnly"
    TAX_AIR_FREIGHT = "TaxAirFreight"
    ELWOOD = "Elwood"


class ScheduleTypes2(Enum):
    INBOUND = 1
    TARGET_AIR_FREIGHT_ONLY = 2
    TAX_AIR_FREIGHT = 16
    ELWOOD = 18

    @classmethod
    def from_string(cls, value: str) -> int:
        """Map a string from ScheduleTypes to the corresponding integer value."""
        mapping = {
            ScheduleTypes.INBOUND.value: cls.INBOUND.value,
            ScheduleTypes.TARGET_AIR_FREIGHT_ONLY.value: cls.TARGET_AIR_FREIGHT_ONLY.value,
            ScheduleTypes.TAX_AIR_FREIGHT.value: cls.TAX_AIR_FREIGHT.value,
            ScheduleTypes.ELWOOD.value: cls.ELWOOD.value,
        }
        if value not in mapping:
            raise ValueError(
                f"Invalid schedule type string: {value}. Must be one of {list(mapping.keys())}"
            )
        return mapping[value]


class LoadTypes(Enum):
    EXPEDITE = "Expedite"
    FAK = "FAK"
    HAZMAT = "HazMat"
    LIVE = "Live"


class LoadTypes2(Enum):
    EXPEDITE = 58
    FAK = 59
    HAZMAT = 60
    LIVE = 61


class AppointmentParams(BaseModel):
    """Query parameters for GET /appointments."""

    status: Optional[int] = None
    startDate: Optional[str] = None
    endDate: Optional[str] = None
    customerId: Optional[str] = None


class ScheduleDetailsParams(BaseModel):
    """Query parameters for GET /schedule/details."""

    schedulePID: int
    weekDate: Optional[str] = None

    @validator("schedulePID")
    def validate_and_transform_schedule_pid(cls, value):
        if isinstance(value, str):
            value = ScheduleTypes2.from_string(value)
        if value not in [e.value for e in ScheduleTypes2]:
            raise ValueError(
                f"Invalid schedulePID, must be one of {[e.value for e in ScheduleTypes2]}"
            )
        return value


class AppointmentPto(BaseModel):
    appointmentID: Optional[int] = None
    appointmentKey: Optional[str] = None
    appointmentPID: int
    appointmentType: AppointmentType
    carrierSCAC: Optional[str] = None
    endTime: str
    notes: Optional[str] = None
    proId: Optional[str] = None
    startTime: str
    status: AppointmentStatus
    trailerID: Optional[str] = None
    updatedAt: str
    updatedBy: Optional[str] = None
    weight: Optional[int] = None
    loadTypePID: Optional[int] = None

    @validator("appointmentType", pre=True)
    def validate_appointment_type(cls, value):
        return AppointmentType.from_string(value)


class AppointmentCreate(BaseModel):
    appointmentKey: str = ""
    carrierSCAC: str
    locationPID: Optional[int] = None
    notes: Optional[str] = None
    proID: str
    schedulePID: Optional[int] = None
    startTime: str
    trailerID: str = ""
    weight: Optional[int] = None
    loadTypePID: Optional[int] = None

    @validator("schedulePID", pre=True)
    def transform_schedule_pid(cls, value):
        if value is None:
            return None
        if isinstance(value, str):
            return ScheduleTypes2.from_string(value)
        return value


class AppointmentUpdate(BaseModel):
    comments: Optional[str] = None
    schedule: ScheduleTypes
    startTime: str


class LocationPto(BaseModel):
    area: Optional[str] = None
    capacity: Optional[int] = None
    facility: Optional[str] = None
    name: Optional[str] = None
    pid: int


class YardViewWarehouse(WarehouseDetails):
    """YardView Warehouse model."""

    area: str
    capacity: int
    facility: str
    name: str
    pid: int


class YardViewGetWarehouseResponse(GetWarehouseResponse):
    """YardView response containing warehouses including open slots."""

    warehouses: Optional[List[YardViewWarehouse]] = None


class ScheduleDetailPto(BaseModel):
    capacity: int
    duration: str
    pid: Optional[int] = None
    startTime: str
    startDate: Optional[str] = ""


class ScacsPto(BaseModel):
    name: str
    id: str


class LoadTypesPto(BaseModel):
    pid: int
    name: str


class YardViewAppointment(BaseModel):
    """YardView appointment model for responses."""

    appointmentId: str
    appointmentPid: Optional[int] = 0
    duration: int
    location: Optional[str] = ""
    notes: Optional[str] = ""
    reference: Optional[str] = ""
    scheduledTime: str
    status: str
    warehouse: Optional[WarehouseDetails] = None
    # Platform-specific data
    extended: Optional[Dict[str, Any]] = None


class YardViewGetOpenSlotsResponse(BaseResponse):
    """YardView-specific response containing open appointment slots."""

    appointments: Optional[List[YardViewAppointment]] = None


class YardViewGetCarrierScacsResponse(BaseResponse):
    """YardView-specific response containing carrier scacs."""

    scacs: Optional[List[ScacsPto]] = None


class YardViewMakeAppointmentResponse(BaseResponse):
    """YardView-specific response containing the created appointment."""

    appointment: Optional[YardViewAppointment] = None


######################################################################
# Selenium
######################################################################


class YardViewAppointmentData(BaseModel):
    """YardView appointment data."""

    appointmentId: Optional[str] = ""
    appointmentKey: str = ""
    facilityId: str = ""
    appointmentTime: Optional[str] = ""
    startTime: Optional[str] = ""
    carrierId: Optional[str] = ""
    dock: Optional[str] = ""
    duration: Optional[int] = 60
    loadId: Optional[str] = ""
    loadType: Optional[str] = ""
    notes: Optional[str] = ""
    status: Optional[str] = ""


######################################################################
# Common
######################################################################


class YardViewLoginRequest(YardViewSchedulingBaseRequest):
    """YardView login request model."""

    action: SchedulingActionType = SchedulingActionType.LOGIN
    platform: SchedulingPlatform = SchedulingPlatform.YARDVIEW

    credentials: Credentials


class YardViewGetCarrierScacsRequest(YardViewSchedulingBaseRequest):
    """YardView-specific GET carrier scacs request."""

    action: SchedulingActionType = SchedulingActionType.GET_CARRIER_SCACS
    platform: SchedulingPlatform = SchedulingPlatform.YARDVIEW


class YardViewGetLoadTypesRequest(YardViewSchedulingBaseRequest):
    """YardView-specific GET load types request."""

    action: SchedulingActionType = SchedulingActionType.GET_LOAD_TYPES
    platform: SchedulingPlatform = SchedulingPlatform.YARDVIEW

    endDate: Optional[str] = ""
    filterType: Optional[str] = ""
    locationId: Optional[str] = ""
    startDate: Optional[str] = ""


class YardViewGetLoadTypesResponse(BaseResponse):
    """YardView-specific response containing load types."""

    loadTypes: Optional[List[LoadTypesPto]] = None


class YardViewGetOpenSlotsRequest(YardViewSchedulingBaseRequest):
    """YardView-specific GET open slots request."""

    action: SchedulingActionType = SchedulingActionType.GET_OPEN_SLOTS
    platform: SchedulingPlatform = SchedulingPlatform.YARDVIEW

    endDate: Optional[str]
    schedulePID: Optional[int] = None
    locationId: Optional[str] = ""
    startDate: Optional[str]
    weekDate: Optional[str] = None

    @validator("schedulePID", pre=True)
    def transform_schedule_pid(cls, value):
        if value is None:
            return None
        if isinstance(value, str):
            return ScheduleTypes2.from_string(value)
        return value


class YardViewGetWarehouseRequest(YardViewSchedulingBaseRequest):
    """YardView-specific GET warehouse request."""

    action: SchedulingActionType = SchedulingActionType.GET_WAREHOUSE
    platform: SchedulingPlatform = SchedulingPlatform.YARDVIEW

    startDate: Optional[str] = ""
    endDate: Optional[str] = ""
    filterType: Optional[str] = ""
    locationId: Optional[str] = ""


class YardViewCancelAppointmentRequest(YardViewSchedulingBaseRequest):
    """YardView-specific cancel appointment request."""

    action: SchedulingActionType = SchedulingActionType.CANCEL_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.YARDVIEW

    appointmentId: str
    endDate: Optional[str] = ""
    reason: Optional[str] = ""
    startDate: Optional[str] = ""


class YardViewGetAppointmentRequest(YardViewSchedulingBaseRequest):
    """YardView-specific get appointment request."""

    action: SchedulingActionType = SchedulingActionType.GET_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.YARDVIEW

    appointmentId: Optional[str] = ""
    customerId: Optional[str] = ""
    endDate: Optional[str] = ""
    startDate: Optional[str] = ""
    status: Optional[AppointmentStatus] = None

    @validator("status", pre=True)
    def validate_status(cls, value):
        return AppointmentStatus.from_string(value)

    def dict(self, **kwargs):
        d = super().dict(**kwargs)
        if "status" in d and d["status"] is not None:
            d["status"] = AppointmentStatus.from_string(d["status"])
        return d


class YardViewMakeAppointmentRequest(YardViewSchedulingBaseRequest):
    """YardView-specific make appointment request."""

    action: SchedulingActionType = SchedulingActionType.MAKE_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.YARDVIEW

    appointment: YardViewAppointmentData | AppointmentCreate

    @validator("appointment", pre=True)
    def validate_appointment(cls, value, values):
        mode = values.get("mode", RequestMode.SELENIUM)
        if mode == RequestMode.API:
            return AppointmentCreate.parse_obj(value)
        elif mode == RequestMode.SELENIUM:
            return YardViewAppointmentData.parse_obj(value)
        raise ValueError(f"Invalid mode: {mode}")


class YardViewUpdateAppointmentRequest(YardViewSchedulingBaseRequest):
    """YardView-specific update appointment request."""

    action: SchedulingActionType = SchedulingActionType.UPDATE_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.YARDVIEW

    appointment: YardViewAppointmentData | AppointmentUpdate
    appointmentId: str
