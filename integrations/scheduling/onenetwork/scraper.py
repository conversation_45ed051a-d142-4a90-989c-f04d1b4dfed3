"""OneNetwork Selenium scheduling implementation."""

import functools
import time
from datetime import datetime, timed<PERSON>ta
from typing import Op<PERSON>, <PERSON>ple, Type

from selenium.webdriver.chrome.webdriver import WebDriver
from selenium.webdriver.common.by import By

from cache import load_cookies, save_cookies
from config import PLATFORM_URLS
from integrations.scheduling.models import (
    Appointment,
    CancelAppointmentResponse,
    Credentials,
    GetAppointmentResponse,
    GetLoadTypesResponse,
    GetWarehouseResponse,
    LoginResponse,
)
from integrations.scheduling.onenetwork.models import (
    OneNetworkAppointmentData,
    OneNetworkCancelAppointmentRequest,
    OneNetworkGetAppointmentRequest,
    OneNetworkGetLoadTypesRequest,
    OneNetworkGetOpenSlotsAppointmentData,
    OneNetworkGetOpenSlotsRequest,
    OneNetworkGetOpenSlotsResponse,
    OneNetworkGetWarehouseRequest,
    OneNetworkLoginRequest,
    OneNetworkMakeAppointmentRequest,
    OneNetworkMakeAppointmentResponse,
    OneNetworkUpdateAppointmentRequest,
    OneNetworkUpdateAppointmentResponse,
)
from integrations.scheduling.onenetwork.utils import (
    access_global_search,
    change_iframe,
    extract_warehouse_details,
    fetch_open_slots,
    input_contact_details,
    switch_to_shipment_details,
    switch_to_site,
    wait_for_element_with_polling,
    wait_for_elements_with_polling,
)
from models.base import BaseResponse
from session import add_cookies, with_driver
from utils.logging import logger
from utils.selenium import (
    get_element_text,
    is_element_present,
    safe_click,
    safe_send_keys,
    wait_for_element,
    wait_for_page_load,
)

# OneNetwork element locators
LOCATORS = {
    # Login indicators
    "username_field": (By.ID, "input-username"),
    "password_field": (By.ID, "input-password"),
    "login_button": (By.ID, "submitButton"),
    "login_error": (By.ID, "errors-list"),
    "username_error": (By.ID, "username-errorCt"),
    "password_error": (By.ID, "password-errorCt"),
    "logged_in_indicator": (By.CLASS_NAME, "GlobalSidebar__container___tmTKr"),
}

ONENETWORK_LOGIN_URL = "https://logon.onenetwork.com/sso/logon.sso"
ONENETWORK_STATIC_URL = (
    "https://logistics.onenetwork.com/oms/img/neo/logo_light.png"
)


def parse_appointment_row(row_element) -> Optional[Appointment]:
    """Parse an appointment row element into an Appointment object.

    Args:
        row_element: Table row WebElement

    Returns:
        Appointment object or None if parsing fails
    """
    # Implementation needed here
    pass


def is_logged_in(driver: WebDriver) -> bool:
    """Check if we're currently logged in.

    Args:
        driver: WebDriver instance

    Returns:
        True if logged in, False otherwise
    """
    try:
        if PLATFORM_URLS["onenetwork"] not in driver.current_url:
            logger.info("Not logged in: Not on OneNetwork main page")
            return False

        indicator_locator = LOCATORS["logged_in_indicator"]
        element = wait_for_element_with_polling(
            driver,
            indicator_locator,
            timeout=5,  # Reduced from effective 5s
            poll_interval=0.2,
        )
        if element:
            logger.info("Logged in indicator found, user is logged in")
            return True
        return False
    except:
        return False


def perform_login(
    driver: WebDriver, credentials: Credentials
) -> Tuple[bool, Optional[str]]:
    """Perform actual login operation.

    Args:
        driver: WebDriver instance
        credentials: User credentials

    Returns:
        True if login successful, False otherwise
    """
    try:
        try:
            element_found = wait_for_element(
                driver, LOCATORS["username_field"]
            )
            if not element_found:
                raise Exception("Username field not found")
        except Exception as ex:
            logger.info(f"Login page not loaded correctly: {ex}")
            driver.get(ONENETWORK_LOGIN_URL)
            wait_for_element(driver, LOCATORS["username_field"])

        safe_send_keys(
            driver, LOCATORS["username_field"], credentials.username
        )
        safe_send_keys(
            driver, LOCATORS["password_field"], credentials.password
        )

        safe_click(driver, LOCATORS["login_button"])
        wait_for_page_load(driver)

        try:
            driver.implicitly_wait(1)

            if is_element_present(driver, LOCATORS["login_error"], timeout=2):
                error_msg = get_element_text(driver, LOCATORS["login_error"])
                logger.error(f"OneNetwork Login failed: {error_msg}")
                return False, error_msg

            if is_element_present(driver, (By.ID, "networklinks"), timeout=3):
                logger.info(
                    "Network selection page detected. Selecting 'Logistics Network (LN)'..."
                )
                ln_button_locator = (
                    By.XPATH,
                    "//li[.//div[contains(text(), 'Logistics Network (LN)')]]//button[text()='Select']",
                )
                ln_button = wait_for_element(driver, ln_button_locator)
                if ln_button:
                    ln_button.click()
                    wait_for_page_load(driver)
                    logger.info(
                        "Successfully selected 'Logistics Network (LN)'."
                    )
                else:
                    raise Exception(
                        "Could not find the 'Logistics Network (LN)' button on the selection page."
                    )

        except Exception:
            # Silently continue if no error message found
            pass

        driver.implicitly_wait(10)
        return is_logged_in(driver), None

    except Exception as e:
        logger.error(f"OneNetwork Login failed with error: {str(e)}")
        return False, str(e)


def ensure_logged_in(
    driver: WebDriver, credentials: Credentials
) -> Tuple[bool, Optional[str]]:
    """Ensure user is logged in, using cached cookies if possible.

    Args:
        driver: WebDriver instance
        credentials: User credentials

    Returns:
        True if logged in successfully, False otherwise
    """
    cookies = load_cookies("onenetwork", credentials.username)
    if cookies:
        logger.info(f"Found saved cookies for user {credentials.username}")
        try:
            driver.get(ONENETWORK_STATIC_URL)

            add_cookies(driver, cookies)

            driver.get(PLATFORM_URLS["onenetwork"])

            if is_logged_in(driver):
                logger.info("Successfully logged in using cached cookies")
                return True, None
            else:
                logger.info("Cached cookies expired or invalid")
        except Exception as e:
            logger.warning(f"Error using cached cookies: {str(e)}")

    logger.info(f"Attempting fresh login for user {credentials.username}")

    try:
        driver.get(PLATFORM_URLS["onenetwork"])

        if perform_login(driver, credentials):
            logger.info(
                f"Login successful, caching cookies for {credentials.username}"
            )
            new_cookies = driver.get_cookies()
            save_cookies("onenetwork", credentials.username, new_cookies)

            retry = 0
            while retry < 3:
                driver.get(PLATFORM_URLS["onenetwork"])

                search_button_locator = (
                    By.CSS_SELECTOR,
                    ".SidebarButton__container___oGMkc.GlobalSidebar__button___UxNzn svg.fa-search",
                )
                try:
                    logger.info("Waiting for search button to be visible")

                    wait_for_element(driver, search_button_locator, timeout=20)

                    logger.info("Search button is now visible")
                except Exception as e:
                    logger.warning(
                        f"Timed out waiting for search button: {str(e)}"
                    )
                    retry += 1
                    logger.info(f"Retrying opening main page : ({retry}/3)")
                    continue

                break

            return True, None
        else:
            logger.error("Login failed")
            return False, None

    except Exception as e:
        logger.error(f"Login process failed with error: {str(e)}")
        return False, str(e)


def requires_login(response_cls: Type[BaseResponse]):
    """Decorator that ensures user is logged in before executing the handler."""

    def decorator(handler):
        @functools.wraps(handler)
        def wrapper(request, driver, *args, **kwargs):
            success, error_msg = ensure_logged_in(driver, request.credentials)
            if not success:
                return response_cls(
                    success=False,
                    message="Authentication failed",
                    errors=[
                        (
                            error_msg
                            if error_msg
                            else "Failed to log in with provided credentials"
                        )
                    ],
                )
            return handler(request, driver, *args, **kwargs)

        return wrapper

    return decorator


@with_driver
def login(request: OneNetworkLoginRequest, driver: WebDriver) -> LoginResponse:
    """Log in to OneNetwork.

    Args:
        request: Login request
        driver: WebDriver instance provided by decorator

    Returns:
        Login response
    """
    try:
        success, error_msg = perform_login(driver, request.credentials)

        if success:
            return LoginResponse(
                success=True,
                message="Successfully logged in",
                userDetails={"username": request.credentials.username},
            )
        else:
            return LoginResponse(
                success=False,
                message="Login failed",
                errors=[error_msg if error_msg else "Unknown login error"],
            )

    except Exception as e:
        return LoginResponse(
            success=False,
            message=f"Error during login: {str(e)}",
            errors=[str(e)],
        )


@with_driver
@requires_login(GetLoadTypesResponse)
def get_load_types(
    request: OneNetworkGetLoadTypesRequest, driver: WebDriver
) -> GetLoadTypesResponse:
    """Get load types from OneNetwork.

    Args:
        request: Get load types request
        driver: WebDriver instance provided by decorator

    Returns:
        Get load types response
    """
    try:
        return GetLoadTypesResponse(
            success=False,
            message="Not implemented yet",
            errors=["Load types fetching not implemented"],
        )
    except Exception as e:
        return GetLoadTypesResponse(
            success=False,
            message="Failed to fetch load types",
            errors=[str(e)],
        )


@with_driver
@requires_login(OneNetworkGetOpenSlotsResponse)
def get_open_slots(
    request: OneNetworkGetOpenSlotsRequest, driver: WebDriver
) -> OneNetworkGetOpenSlotsResponse:
    """Get open appointment slots from OneNetwork.

    Args:
        request: Get open slots request
        driver: WebDriver instance provided by decorator

    Returns:
        Get open slots response
    """
    try:
        ship_no = request.shipNo[0] if request.shipNo else ""
        ref_no = request.refNo
        start_date = datetime.strptime(
            request.startDate, "%Y-%m-%dT%H:%M:%S.%fZ"
        )
        end_date = datetime.strptime(request.endDate, "%Y-%m-%dT%H:%M:%S.%fZ")

        available_slots = []
        appointments = []

        search_completed = access_global_search(driver, ship_no, ref_no)
        if not search_completed:
            logger.error(
                f"Failed to access global search for shipNo: {ship_no}, refNo: {ref_no}"
            )
            return OneNetworkGetOpenSlotsResponse(
                success=False,
                message="Failed to access search functionality",
                errors=["Failed to access search functionality"],
            )

        shipment_details = switch_to_shipment_details(driver)
        if not shipment_details:
            logger.error(
                f"Failed to access shipment details for shipNo: {ship_no}, refNo: {ref_no}"
            )
            return OneNetworkGetOpenSlotsResponse(
                success=False,
                message="Failed to access shipment details",
                errors=["Failed to access shipment details"],
            )

        reschedule_button_locator = (
            By.XPATH,
            "//button[contains(@class, 'Button__primary') "
            "and .//span[contains(text(), 'Reschedule Appointment')] "
            "and not(ancestor::div[contains(@class, 'Stash__hidden___RJfpz')])]",
        )
        reschedule_button = wait_for_element_with_polling(
            driver, reschedule_button_locator, timeout=2
        )

        if reschedule_button:
            logger.info("Clicking on Reschedule Appointment button")
            # reschedule_button.click()
            driver.execute_script("arguments[0].click();", reschedule_button)

            # Wait for the popup to appear
            wait_for_page_load(driver)

            # Open the reason code dropdown
            reason_dropdown_locator = (
                By.XPATH,
                "//div[contains(@class, 'ComboField__trigger___B61kB') and ./ancestor::div[contains(., 'Reason Code')]]",
            )
            reason_dropdown = wait_for_element_with_polling(
                driver, reason_dropdown_locator, timeout=10
            )

            if not reason_dropdown:
                return OneNetworkGetOpenSlotsResponse(
                    success=False,
                    message="Reason code dropdown not found",
                    errors=["Could not find the reason code dropdown"],
                )

            logger.info("Opening reason code dropdown")
            reason_dropdown.click()

            # Select "Rescheduled for Carrier or Driver" option
            carrier_option_locator = (
                By.XPATH,
                "//div[contains(@class, 'DropdownList__listItem') and .//div[contains(text(), 'Rescheduled for Carrier or Driver')]]",
            )
            carrier_option = wait_for_element_with_polling(
                driver, carrier_option_locator, timeout=10
            )

            if not carrier_option:
                return OneNetworkGetOpenSlotsResponse(
                    success=False,
                    message="Carrier option not found",
                    errors=[
                        "Could not find 'Rescheduled for Carrier or Driver' option"
                    ],
                )

            logger.info("Selecting 'Rescheduled for Carrier or Driver' option")
            carrier_option.click()

            # Click on the final Reschedule Appointment button in the popup
            final_reschedule_button_locator = (
                By.XPATH,
                "//button[contains(@class, 'Button__primary') and .//span[contains(text(), 'Reschedule Appointment')]]",
            )
            final_reschedule_button = wait_for_elements_with_polling(
                driver,
                final_reschedule_button_locator,
                timeout=10,
            )

            if not final_reschedule_button:
                return OneNetworkGetOpenSlotsResponse(
                    success=False,
                    message="Final reschedule button not found",
                    errors=[
                        "Could not find the final Reschedule Appointment button"
                    ],
                )

            logger.info("Clicking on final Reschedule Appointment button")
            final_reschedule_button = final_reschedule_button[1]
            final_reschedule_button.click()

            iframe_locator3 = (
                By.CSS_SELECTOR,
                "iframe.Perspective__frame___iATIU",
            )
            change_iframe(driver, iframe_locator3, index=-1)

        logger.info(
            f"Fetching all visible slots starting from {start_date.date()}..."
        )
        formatted_start_date = start_date.strftime("%b %-d, %Y %-I:%M %p")

        all_visible_slots_result = fetch_open_slots(
            driver, start_date, formatted_start_date
        )

        if (
            all_visible_slots_result.success
            and all_visible_slots_result.openSlots
        ):
            for slot in sorted(
                all_visible_slots_result.openSlots,
                key=lambda x: x.scheduledTime,
            ):
                slot_dt = datetime.strptime(
                    slot.scheduledTime, "%Y-%m-%dT%H:%M:%S"
                )

                if slot_dt.date() > end_date.date():
                    logger.info(
                        f"Stopping search as slot {slot.scheduledTime} is past end date {end_date.date()}."
                    )
                    break

                if start_date.date() <= slot_dt.date() <= end_date.date():
                    available_slots.append(slot)

        try:
            switched = switch_to_site(driver)
            if not switched:
                logger.error("Failed to switch to site")
                logger.info("Can't fetch warehouse details.")

            warehouse_details = extract_warehouse_details(
                driver, available_slots
            )

            if warehouse_details:
                appointment_data = OneNetworkGetOpenSlotsAppointmentData(
                    warehouse=warehouse_details,
                    notes="Open slots available for the respective warehouse.",
                    status="AVAILABLE",
                )

                appointments.append(appointment_data)
        except Exception as e:
            logger.error(f"Error clicking on warehouse element: {str(e)}")

        return OneNetworkGetOpenSlotsResponse(
            success=True,
            message="Successfully retrieved open slots",
            appointments=appointments,
        )

    except Exception as e:
        return OneNetworkGetOpenSlotsResponse(
            success=False,
            message="Failed to fetch open slots",
            errors=[str(e)],
        )


@with_driver
@requires_login(GetWarehouseResponse)
def get_warehouse(
    request: OneNetworkGetWarehouseRequest, driver: WebDriver
) -> GetWarehouseResponse:
    """Get warehouse information from OneNetwork.

    Args:
        request: Get warehouse request
        driver: WebDriver instance provided by decorator

    Returns:
        Get warehouse response
    """
    try:
        return GetWarehouseResponse(
            success=False,
            message="Not implemented yet",
            errors=["Warehouse fetching not implemented"],
        )
    except Exception as e:
        return GetWarehouseResponse(
            success=False, message="Failed to fetch warehouse", errors=[str(e)]
        )


@with_driver
@requires_login(CancelAppointmentResponse)
def cancel_appointment(
    request: OneNetworkCancelAppointmentRequest, driver: WebDriver
) -> CancelAppointmentResponse:
    """Cancel an appointment in OneNetwork.

    Args:
        request: Cancel appointment request
        driver: WebDriver instance provided by decorator

    Returns:
        Cancel appointment response
    """
    try:
        ship_no = request.shipNo
        ref_no = request.refNo
        reason = request.reason

        search_completed = access_global_search(driver, ship_no, ref_no)
        if not search_completed:
            logger.error(
                f"Failed to access global search for shipNo: {ship_no}, refNo: {ref_no}"
            )
            return CancelAppointmentResponse(
                success=False,
                message="Failed to access search functionality",
                errors=["Failed to access search functionality"],
            )

        shipment_details = switch_to_shipment_details(driver)
        if not shipment_details:
            logger.error(
                f"Failed to access shipment details for shipNo: {ship_no}, refNo: {ref_no}"
            )
            return CancelAppointmentResponse(
                success=False,
                message="Failed to access shipment details",
                errors=["Failed to access shipment details"],
            )

        cancel_button_locator = (
            By.XPATH,
            "//button[contains(@class, 'Button__primary___FSeua') and .//span[contains(text(), 'Cancel Appointment')]]",
        )
        cancel_button = wait_for_element_with_polling(
            driver, cancel_button_locator, timeout=10
        )

        if not cancel_button:
            return CancelAppointmentResponse(
                success=False,
                message="Cancel button not found",
                errors=["Could not find the Cancel Appointment button"],
            )

        logger.info("Clicking on Cancel Appointment button")
        cancel_button.click()

        # Wait for confirmation dialog
        wait_for_page_load(driver)

        # Select reason code from dropdown
        reason_dropdown_locator = (
            By.CSS_SELECTOR,
            ".ComboField__trigger___B61kB",
        )
        reason_dropdown = wait_for_element_with_polling(
            driver, reason_dropdown_locator, timeout=10
        )

        if not reason_dropdown:
            return CancelAppointmentResponse(
                success=False,
                message="Reason code dropdown not found",
                errors=["Could not find the reason code dropdown"],
            )

        logger.info("Clicking on reason code dropdown")
        reason_dropdown.click()

        # Select "Canceled for Other" option
        reason_option_locator = (
            By.XPATH,
            "//div[contains(@class, 'DropdownList__listItem') and .//div[contains(text(), 'Canceled for Other')]]",
        )
        reason_option = wait_for_element_with_polling(
            driver, reason_option_locator, timeout=10
        )

        if not reason_option:
            return CancelAppointmentResponse(
                success=False,
                message="Reason option not found",
                errors=["Could not find 'Canceled for Other' option"],
            )

        logger.info("Selecting 'Canceled for Other' option")
        reason_option.click()

        # Enter description/reason
        description_input_locator = (
            By.CSS_SELECTOR,
            "textarea[name='reasonDescription']",
        )
        description_input = wait_for_element_with_polling(
            driver, description_input_locator, timeout=10
        )

        if not description_input:
            return CancelAppointmentResponse(
                success=False,
                message="Description field not found",
                errors=["Could not find the description input field"],
            )

        logger.info(f"Entering cancellation reason: {reason}")
        description_input.clear()
        description_input.send_keys(reason or "No reason provided")

        # Find and click the confirm button
        confirm_button_locator = (
            By.XPATH,
            "//button[contains(@class, 'Button__primary___FSeua') and .//span[contains(text(), 'Confirm')]]",
        )
        confirm_button = wait_for_element_with_polling(
            driver, confirm_button_locator, timeout=10
        )

        if not confirm_button:
            return CancelAppointmentResponse(
                success=False,
                message="Confirm button not found",
                errors=[
                    "Could not find the Confirm button in cancellation dialog"
                ],
            )

        logger.info("Clicking on Confirm button")
        confirm_button.click()

        # Wait for cancellation to complete
        time.sleep(1)

        return CancelAppointmentResponse(
            success=True,
            message="Appointment successfully cancelled",
        )
    except Exception as e:
        return CancelAppointmentResponse(
            success=False,
            message="Failed to cancel appointment",
            errors=[str(e)],
        )


@with_driver
@requires_login(GetAppointmentResponse)
def get_appointment(
    request: OneNetworkGetAppointmentRequest, driver: WebDriver
) -> GetAppointmentResponse:
    """Get an appointment from OneNetwork.

    Args:
        request: Get appointment request
        driver: WebDriver instance provided by decorator

    Returns:
        Get appointment response
    """
    try:
        ship_no = request.shipNo
        ref_no = request.refNo

        search_completed = access_global_search(driver, ship_no, ref_no)
        if not search_completed:
            logger.error(
                f"Failed to access global search for shipNo: {ship_no}, refNo: {ref_no}"
            )
            return GetAppointmentResponse(
                success=False,
                message="Failed to access search functionality",
                errors=["Failed to access search functionality"],
            )

        filter_toolbar_locator = (By.CSS_SELECTOR, ".filter-plugin-toolbar")
        wait_for_element_with_polling(
            driver, filter_toolbar_locator, timeout=10
        )

        grid_locator = (By.CSS_SELECTOR, ".x-grid3-body")
        grid = driver.find_elements(*grid_locator)[1]

        appointment_link_locator = (By.CSS_SELECTOR, ".appointment-link")
        appointment_links = driver.find_elements(*appointment_link_locator)

        if not appointment_links:
            logger.warning("No appointment links found in search results")

        # Extract details from the first row of the table
        try:
            consignee_cell = grid.find_element(
                By.CSS_SELECTOR,
                "td.x-grid3-td-Undefined\\$Consignee div.x-grid3-cell-inner",
            )
            consignee = consignee_cell.text.strip()

            # Extract target delivery
            delivery_cell = grid.find_element(
                By.CSS_SELECTOR,
                "td.x-grid3-td-Undefined\\$TargetDelivery\\.ActualDelivery div.x-grid3-cell-inner",
            )
            target_delivery = delivery_cell.text.strip()

            # Extract status
            status_cell = grid.find_element(
                By.CSS_SELECTOR,
                "td.x-grid3-td-Undefined\\$State\\.StatusReason div.x-grid3-cell-inner",
            )
            status = status_cell.text.strip().replace("/", "").split("\n")[0]

            # Extract Eq/Stops/Size
            eq_stops_size_cell = grid.find_element(
                By.CSS_SELECTOR,
                "td.x-grid3-td-Undefined\\$EQUIP\\.STOP\\.SZ div.x-grid3-cell-inner",
            )
            eq_stops_size = eq_stops_size_cell.text.strip()

            # Extract carrier
            carrier_cell = grid.find_element(
                By.CSS_SELECTOR,
                "td.x-grid3-td-Undefined\\$CarrierForStatusComposite div.x-grid3-cell-inner",
            )
            carrier = carrier_cell.text.strip()

            # Extract ship_to_addr
            # ship_to_addr_cell = grid.find_element(By.CSS_SELECTOR, "td.x-grid3-td-Undefined\\$ShipToAddressString div.x-grid3-cell-inner")
            # ship_to_addr = ship_to_addr_cell.text.strip()

            # Extract reservation_id (from appointment link if available)
            reservation_id = ""
            delivery_cell = grid.find_element(
                By.CSS_SELECTOR,
                "td.x-grid3-td-Undefined\\$Delivery div.x-grid3-cell-inner",
            )
            appointment_link = (
                delivery_cell.find_element(
                    By.CSS_SELECTOR, "a.appointment-link"
                )
                if "appointment-link"
                in delivery_cell.get_attribute("innerHTML")
                else None
            )

            if appointment_link:
                link_text = appointment_link.text.strip()
                reservation_id = link_text.split()[0] if link_text else ""

            # Click on consignee to get warehouse details
            consignee_link = consignee_cell.find_element(By.TAG_NAME, "a")
            warehouse_details = ""

            if consignee_link:
                consignee_link.click()

                # Switch to the iframe containing address details
                iframe_locator3 = (
                    By.CSS_SELECTOR,
                    "iframe.Perspective__frame___iATIU",
                )
                change_iframe(driver, iframe_locator3, index=-1)

                # Extract warehouse details
                warehouse_details = extract_warehouse_details(driver, [])

            # Create appointment object
            appointment = Appointment(
                appointmentId=reservation_id,
                scheduledTime=target_delivery,
                status=status,
                duration=60,  # Default duration in minutes
                notes=f"Carrier: {carrier}, Equipment: {eq_stops_size}",
                warehouse=warehouse_details,
                reference=consignee,
            )

            return GetAppointmentResponse(
                success=True,
                message=f"Successfully retrieved appointment details for {ship_no or ref_no}",
                appointments=[appointment],
            )

        except Exception as e:
            logger.error(
                f"Error extracting appointment details from table: {str(e)}"
            )
            return GetAppointmentResponse(
                success=False,
                message="Failed to extract appointment details",
                errors=[str(e)],
            )

    except Exception as e:
        logger.error(f"Failed to get appointment: {str(e)}")
        return GetAppointmentResponse(
            success=False, message="Failed to get appointment", errors=[str(e)]
        )


@with_driver
@requires_login(OneNetworkMakeAppointmentResponse)
def make_appointment(
    request: OneNetworkMakeAppointmentRequest, driver: WebDriver
) -> OneNetworkMakeAppointmentResponse:
    """Create an appointment in OneNetwork.

    Args:
        request: Make appointment request
        driver: WebDriver instance provided by decorator

    Returns:
        Make appointment response
    """
    try:
        appointments = []
        all_errors = []
        for appointment in request.appointments:
            failed_appointment = OneNetworkAppointmentData(
                scheduledTime=appointment.scheduledTime,
                status="FAILED",
                duration=appointment.duration,
                notes=None,
                warehouse=(
                    appointment.warehouse
                    if hasattr(appointment, "warehouse")
                    else None
                ),
                refNo=appointment.refNo,
                shipNo=appointment.shipNo,
                contact=appointment.contact,
            )

            ship_no = appointment.shipNo[0] if appointment.shipNo else ""
            ref_no = appointment.refNo
            contact_details = appointment.contact

            try:
                appointment_datetime = datetime.strptime(
                    appointment.scheduledTime, "%Y-%m-%dT%H:%M:%S.%fZ"
                )

                # Format the date for the date picker (e.g., "May 20, 2025 10:30 AM")
                formatted_date = appointment_datetime.strftime(
                    "%b %-d, %Y %-I:%M %p"
                )

                search_completed = access_global_search(
                    driver, ship_no, ref_no
                )
                if not search_completed:
                    logger.error(
                        f"Failed to access global search for shipNo: {ship_no}, refNo: {ref_no}"
                    )
                    # Create a failed appointment object
                    failed_appointment.notes = (
                        "Failed to access search functionality"
                    )
                    all_errors.append(failed_appointment.notes)
                    appointments.append(failed_appointment)
                    continue

                shipment_details = switch_to_shipment_details(driver)
                if not shipment_details:
                    logger.error(
                        f"Failed to access shipment details for shipNo: {ship_no}, refNo: {ref_no}"
                    )
                    # Create a failed appointment object
                    failed_appointment.notes = (
                        "Failed to access shipment details"
                    )
                    all_errors.append(failed_appointment.notes)
                    appointments.append(failed_appointment)
                    continue

                schedule_button_locator_confirmed = (
                    By.XPATH,
                    "//button[contains(@class, 'Button__primary') "
                    "and .//span[contains(text(), 'Schedule Confirmed Appointment')] "
                    "and not(ancestor::div[contains(@class, 'Stash__hidden___RJfpz')])]",
                )

                schedule_button_locator = (
                    By.XPATH,
                    "//button[contains(@class, 'Button__primary') "
                    "and .//span[contains(text(), 'Schedule Appointment')] "
                    "and not(ancestor::div[contains(@class, 'Stash__hidden___RJfpz')])]",
                )

                schedule_button = wait_for_element_with_polling(
                    driver, schedule_button_locator_confirmed, timeout=10
                )

                if not schedule_button:
                    logger.warning(
                        "Schedule Confirmed Appointment button not found, trying Schedule Appointment button"
                    )
                    schedule_button = wait_for_element_with_polling(
                        driver, schedule_button_locator, timeout=10
                    )

                if not schedule_button:
                    logger.error("Neither Schedule button found")
                    failed_appointment.notes = (
                        "Could not find the Schedule Appointment button"
                    )
                    all_errors.append(failed_appointment.notes)
                    appointments.append(failed_appointment)
                    continue

                # date_set = set_target_date(driver, formatted_date)
                # if not date_set:
                #     logger.error(f"Failed to set date to {formatted_date}")
                #     failed_appointment.notes = "Failed to set target date"
                #     appointments.append(failed_appointment)
                #     continue

                available_open_slots = fetch_open_slots(
                    driver,
                    appointment_datetime,
                    formatted_date,
                    match_row=True,
                )
                if not available_open_slots.success:
                    logger.error(
                        f"Failed to find open slots for date {formatted_date}"
                    )
                    failed_appointment.notes = "Failed to find open slots"
                    all_errors.append(failed_appointment.notes)
                    appointments.append(failed_appointment)
                    continue

                logger.info("Clicking on schedule Appointment button")

                input_contact_details(driver, contact_details)

                # Restricted click on the buttons, due to which using JS to click.
                driver.execute_script("arguments[0].click();", schedule_button)

                # Wait for the popup to appear
                wait_for_page_load(driver)

                iframe_locator3 = (
                    By.CSS_SELECTOR,
                    "iframe.Perspective__frame___iATIU",
                )
                change_iframe(driver, iframe_locator3, index=-1)

                switched = switch_to_site(driver)
                warehouse_details = None
                if not switched:
                    logger.error("Failed to switch to site")
                    logger.info("Can't fetch warehouse details.")
                else:
                    warehouse_details = extract_warehouse_details(driver)

                appointments.append(
                    OneNetworkAppointmentData(
                        scheduledTime=appointment.scheduledTime,
                        duration=appointment.duration,
                        status="SCHEDULED",
                        notes="Appointment created successfully",
                        warehouse=warehouse_details,
                        refNo=appointment.refNo,
                        shipNo=appointment.shipNo,
                        contact=appointment.contact,
                    )
                )
            except Exception as e:
                logger.error(f"Error processing appointment: {str(e)}")
                failed_appointment.notes = (
                    f"Error while creating appointment: {str(e)}"
                )
                all_errors.append(failed_appointment.notes)
                appointments.append(failed_appointment)

        if not all_errors:
            return OneNetworkMakeAppointmentResponse(
                success=True,
                message="All appointments scheduled successfully",
                appointments=appointments,
            )

        return OneNetworkMakeAppointmentResponse(
            success=False,
            message="Appointments partially completed.",
            appointments=appointments,
            errors=all_errors,
        )
    except Exception as e:
        return OneNetworkMakeAppointmentResponse(
            success=False,
            message="Failed to make appointment",
            errors=[str(e)],
        )


@with_driver
@requires_login(OneNetworkUpdateAppointmentResponse)
def update_appointment(
    request: OneNetworkUpdateAppointmentRequest, driver: WebDriver
) -> OneNetworkUpdateAppointmentResponse:
    """Update an appointment in OneNetwork.

    Args:
        request: Update appointment request
        driver: WebDriver instance provided by decorator

    Returns:
        Update appointment response
    """
    try:
        appointments = []
        all_errors = []

        for appointment in request.appointments:
            failed_appointment = OneNetworkAppointmentData(
                scheduledTime=appointment.scheduledTime,
                status="FAILED",
                duration=appointment.duration,
                notes=None,
                warehouse=(
                    appointment.warehouse
                    if hasattr(appointment, "warehouse")
                    else None
                ),
                refNo=appointment.refNo,
                shipNo=appointment.shipNo,
                contact=appointment.contact,
            )

            ship_no = appointment.shipNo[0] if appointment.shipNo else ""
            ref_no = appointment.refNo

            try:
                appointment_datetime = datetime.strptime(
                    appointment.scheduledTime, "%Y-%m-%dT%H:%M:%S.%fZ"
                )

                # Format the date for the date picker (e.g., "May 20, 2025 10:30 AM")
                formatted_date = appointment_datetime.strftime(
                    "%b %-d, %Y %-I:%M %p"
                )

                search_completed = access_global_search(
                    driver, ship_no, ref_no
                )
                if not search_completed:
                    logger.error(
                        f"Failed to access global search for shipNo: {ship_no}, refNo: {ref_no}"
                    )
                    # Create a failed appointment object
                    failed_appointment.notes = (
                        "Failed to access search functionality"
                    )
                    all_errors.append(failed_appointment.notes)
                    appointments.append(failed_appointment)
                    continue

                shipment_details = switch_to_shipment_details(driver)
                if not shipment_details:
                    logger.error(
                        f"Failed to access shipment details for shipNo: {ship_no}, refNo: {ref_no}"
                    )
                    # Create a failed appointment object
                    failed_appointment.notes = (
                        "Failed to access shipment details"
                    )
                    all_errors.append(failed_appointment.notes)
                    appointments.append(failed_appointment)
                    continue

                reschedule_button_locator = (
                    By.XPATH,
                    "//button[contains(@class, 'Button__primary') and .//span[contains(text(), 'Reschedule Appointment')]]",
                )
                reschedule_button = wait_for_element_with_polling(
                    driver, reschedule_button_locator, timeout=10
                )

                if not reschedule_button:
                    logger.error("Reschedule button not found")
                    failed_appointment.notes = (
                        "Could not find the Reschedule Appointment button"
                    )
                    all_errors.append(failed_appointment.notes)
                    appointments.append(failed_appointment)
                    continue

                logger.info("Clicking on Reschedule Appointment button")
                # Direct click restricted so using JS to click.
                driver.execute_script(
                    "arguments[0].click();", reschedule_button
                )

                # Wait for the popup to appear
                wait_for_page_load(driver)

                # Open the reason code dropdown
                reason_dropdown_locator = (
                    By.XPATH,
                    "//div[contains(@class, 'ComboField__trigger___B61kB') and ./ancestor::div[contains(., 'Reason Code')]]",
                )
                reason_dropdown = wait_for_element_with_polling(
                    driver,
                    reason_dropdown_locator,
                    timeout=10,
                )

                if not reason_dropdown:
                    logger.error("Reason code dropdown not found")
                    failed_appointment.notes = (
                        "Could not find the reason code dropdown"
                    )
                    appointments.append(failed_appointment)
                    continue

                logger.info("Opening reason code dropdown")
                reason_dropdown.click()

                # Select "Rescheduled for Carrier or Driver" option
                carrier_option_locator = (
                    By.XPATH,
                    "//div[contains(@class, 'DropdownList__listItem') and .//div[contains(text(), 'Rescheduled for Carrier or Driver')]]",
                )
                carrier_option = wait_for_element_with_polling(
                    driver,
                    carrier_option_locator,
                    timeout=10,
                )

                if not carrier_option:
                    logger.error("Carrier option not found")
                    failed_appointment.notes = "Could not find 'Rescheduled for Carrier or Driver' option"
                    all_errors.append(failed_appointment.notes)
                    appointments.append(failed_appointment)
                    continue

                logger.info(
                    "Selecting 'Rescheduled for Carrier or Driver' option"
                )
                carrier_option.click()

                # Click on the final Reschedule Appointment button in the popup
                final_reschedule_button_locator = (
                    By.XPATH,
                    "//button[contains(@class, 'Button__primary') and .//span[contains(text(), 'Reschedule Appointment')]]",
                )
                final_reschedule_button = wait_for_elements_with_polling(
                    driver, final_reschedule_button_locator, timeout=10
                )

                if (
                    not final_reschedule_button
                    or len(final_reschedule_button) < 2
                ):
                    logger.error("Popup reschedule button not found")
                    failed_appointment.notes = "Could not find the popup Reschedule Appointment button"
                    all_errors.append(failed_appointment.notes)
                    appointments.append(failed_appointment)
                    continue

                logger.info("Clicking on final Reschedule Appointment button")
                final_reschedule_button[1].click()

                iframe_locator3 = (
                    By.CSS_SELECTOR,
                    "iframe.Perspective__frame___iATIU",
                )
                change_iframe(driver, iframe_locator3, index=-1)

                # date_set = set_target_date(driver, formatted_date)
                # if not date_set:
                #     logger.error(f"Failed to set date to {formatted_date}")
                #     failed_appointment.notes = "Failed to set target date"
                #     appointments.append(failed_appointment)
                #     continue

                available_open_slots = fetch_open_slots(
                    driver,
                    appointment_datetime,
                    formatted_date,
                    match_row=True,
                )
                if not available_open_slots.success:
                    logger.error(
                        f"Failed to find open slots for date {formatted_date}"
                    )
                    failed_appointment.notes = "Failed to find open slots"
                    all_errors.append(failed_appointment.notes)
                    appointments.append(failed_appointment)
                    continue

                save_button_locator = (
                    By.XPATH,
                    "//button[.//span[text()='Save']]",
                )
                save_button = wait_for_element_with_polling(
                    driver, save_button_locator, timeout=2
                )

                if not save_button:
                    logger.error("Save button not found")
                    failed_appointment.notes = "Could not find the Save button"
                    all_errors.append(failed_appointment.notes)
                    appointments.append(failed_appointment)
                    continue

                logger.info("Clicking on schedule Appointment button")
                # Restricted click on the buttons, due to which using JS to click.
                driver.execute_script("arguments[0].click();", save_button)

                # Wait for the popup to appear
                wait_for_page_load(driver)

                iframe_locator3 = (
                    By.CSS_SELECTOR,
                    "iframe.Perspective__frame___iATIU",
                )
                change_iframe(driver, iframe_locator3, index=-1)

                switched = switch_to_site(driver)
                warehouse_details = None
                if not switched:
                    logger.error("Failed to switch to site")
                    logger.info("Can't fetch warehouse details.")
                else:
                    warehouse_details = extract_warehouse_details(driver)

                appointments.append(
                    OneNetworkAppointmentData(
                        scheduledTime=appointment.scheduledTime,
                        duration=appointment.duration,
                        status="SCHEDULED",
                        notes="Appointment created successfully",
                        warehouse=warehouse_details,
                        refNo=appointment.refNo,
                        shipNo=appointment.shipNo,
                        contact=appointment.contact,
                    )
                )
            except Exception as e:
                logger.error(f"Error updating appointment: {str(e)}")
                failed_appointment.notes = (
                    f"Error updating appointment: {str(e)}"
                )
                all_errors.append(failed_appointment.notes)
                appointments.append(failed_appointment)
                continue

        if not all_errors:
            return OneNetworkUpdateAppointmentResponse(
                success=True,
                message="All appointments updated successfully",
                appointments=appointments,
            )

        return OneNetworkUpdateAppointmentResponse(
            success=False,
            message="Appointments partially completed.",
            errors=all_errors,
            appointments=appointments,
        )
    except Exception as e:
        return OneNetworkUpdateAppointmentResponse(
            success=False,
            message="Failed to update appointments",
            errors=[str(e)],
        )
