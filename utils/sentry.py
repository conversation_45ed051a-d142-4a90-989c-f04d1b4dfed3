"""Sentry initialization and configuration."""

from typing import Dict, Iterator, Optional, Sequence, Set

import sentry_sdk
from sentry_sdk.integrations.fastapi import FastApiIntegration
from sentry_sdk.integrations.starlette import StarletteIntegration
from sentry_sdk.types import Event, Hint

from config import APP_ENV, SENTRY_DSN
from utils.logging import logger

NOISE_RULES: Dict[str, Sequence[str]] = {
    "pdf": (
        "unable to get page count",
        "may not be a pdf file",
        "couldn't find trailer dictionary",
        "couldn't read xref table",
        "syntax warning",
    ),
}

_ACTIVE_NOISE_CATEGORIES: Set[str] = set(NOISE_RULES.keys())


def add_noise_rules(category: str, substrings: Sequence[str]) -> None:
    """Add or extend a noise category with new substrings."""
    key = category.strip().lower()
    if not key or not substrings:
        return

    existing = list(NOISE_RULES.get(key, ()))
    existing.extend(s for s in substrings if s)
    NOISE_RULES[key] = tuple(s.lower() for s in existing)

    _ACTIVE_NOISE_CATEGORIES.add(key)


def set_noise_categories(categories: Sequence[str]) -> None:
    """Explicitly set which categories are active."""
    if not categories:
        _ACTIVE_NOISE_CATEGORIES.clear()
        _ACTIVE_NOISE_CATEGORIES.update(NOISE_RULES.keys())
        return

    wanted = {c.strip().lower() for c in categories if c and c.strip()}
    known = {k.lower() for k in NOISE_RULES.keys()}
    active = wanted & known
    if active:
        _ACTIVE_NOISE_CATEGORIES.clear()
        _ACTIVE_NOISE_CATEGORIES.update(active)


def _contains_any(substrings: Sequence[str], text: Optional[str]) -> bool:
    if not text:
        return False
    t = text.lower()
    return any(s in t for s in substrings)


def _iter_event_texts(event: Event) -> Iterator[str]:
    if not isinstance(event, dict):
        return

    msg = event.get("message")
    if isinstance(msg, str):
        yield msg

    exc_ctx = event.get("exception")
    if isinstance(exc_ctx, dict):
        values = exc_ctx.get("values")
        if isinstance(values, list):
            for item in values:
                if isinstance(item, dict):
                    v = item.get("value")
                    if isinstance(v, str):
                        yield v
                    t = item.get("type")
                    if isinstance(t, str):
                        yield t
                else:
                    try:
                        yield str(item)
                    except Exception:
                        pass


def _matched_noise_categories(
    event: Event, enabled_categories: Set[str]
) -> Set[str]:
    """Return the set of categories for which any substring matched."""

    matched: Set[str] = set()
    if not enabled_categories:
        return matched

    texts = list(_iter_event_texts(event))
    if not texts:
        return matched

    for category in enabled_categories:
        patterns = NOISE_RULES.get(category, ())
        if not patterns:
            continue
        if any(_contains_any(patterns, text) for text in texts):
            matched.add(category)

    return matched


def init_sentry() -> None:
    """Initialize Sentry SDK."""

    if not SENTRY_DSN:
        if APP_ENV != "development":
            logger.warning("Sentry not initialized without DSN")
        return

    enabled_categories = set(_ACTIVE_NOISE_CATEGORIES)

    def before_send(event: Event, hint: Hint) -> Event | None:
        try:
            categories = _matched_noise_categories(event, enabled_categories)
            if categories:
                event_id = event.get("event_id", "?")
                cats = ",".join(sorted(categories))
                logger.debug(
                    f"Dropping Sentry event {event_id} via noise filter "
                    f"(categories={cats})"
                )
                return None
        except Exception:
            pass
        return event

    sentry_sdk.init(
        dsn=SENTRY_DSN,
        environment=APP_ENV,
        traces_sample_rate=1.0,
        profiles_sample_rate=1.0,
        integrations=[
            StarletteIntegration(
                transaction_style="endpoint",
                failed_request_status_codes={403, *range(500, 599)},
                http_methods_to_capture=("GET",),
            ),
            FastApiIntegration(
                transaction_style="endpoint",
                failed_request_status_codes={403, *range(500, 599)},
                http_methods_to_capture=("GET",),
            ),
        ],
        before_send=before_send,
    )

    logger.info(
        f"Sentry initialized (environment={APP_ENV}, noise_categories={','.join(sorted(enabled_categories)) or '(none)'})",
    )
