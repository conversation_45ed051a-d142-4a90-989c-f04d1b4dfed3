"""Manhattan-specific data models."""

from datetime import datetime
from enum import Enum
from typing import List, Optional

from pydantic import BaseModel, model_validator

from integrations.scheduling.models import (
    Credentials,
    SchedulingActionType,
    SchedulingBaseRequest,
    SchedulingPlatform,
)
from models.base import BaseResponse


class AppointmentType(Enum):
    DROP_EMPTY = "Drop Empty"
    DROP_UNLOAD = "Drop Unload"
    LIVE_LOAD = "Live Load"
    LIVE_UNLOAD = "Live Unload"
    PICKUP_EMPTY = "Pickup Empty"
    PICKUP_UNLOAD = "Pickup Unload"


class Conditions(Enum):
    VISIBLE = "Visible"
    PRESENT = "Present"
    INVISIBLE = "Invisible"
    SELECTED = "Selected"
    ENABLED = "Enabled"
    CLICKABLE = "Clickable"
    NOT_PRESENT = "Not Present"
    BODY_NOT_EMPTY = "Body Not Empty"


class ManhattanAppointmentData(BaseModel):
    """Manhattan appointment data."""

    appointmentId: Optional[str] = ""
    poId: str
    appointmentTime: Optional[str] = ""
    duration: Optional[int] = 60
    notes: Optional[str] = ""
    status: Optional[str] = ""
    appointmentType: Optional[str] = ""
    facilityId: Optional[str] = ""
    facilityText: Optional[str] = ""

    @model_validator(mode="after")
    def validate_appointment_data(self):
        try:
            if self.appointmentTime:
                datetime.strptime(self.appointmentTime, "%Y-%m-%dT%H:%M:%S")
        except ValueError:
            raise ValueError(
                f"appointmentTime must be in format: 'YYYY-MM-DDTHH:MM:SS' (e.g. 2025-07-18T21:00:00), got: '{self.appointmentTime}'"
            )

        try:
            if self.appointmentType:
                AppointmentType(self.appointmentType)
        except ValueError:
            raise ValueError(
                f"Invalid appointmentType: '{self.appointmentType}'. Must be one of: {', '.join([t.value for t in AppointmentType])}"
            )

        return self


class ManhattanValidateAppointmentWarehouse(BaseModel):
    city: Optional[str] = "*"
    state: Optional[str] = "*"


class ManhattanLoginRequest(SchedulingBaseRequest):
    """Manhattan login request model."""

    action: SchedulingActionType = SchedulingActionType.LOGIN
    platform: SchedulingPlatform = SchedulingPlatform.MANHATTAN

    credentials: Credentials


class ManhattanGetLoadTypesRequest(SchedulingBaseRequest):
    """Manhattan-specific GET load types request."""

    action: SchedulingActionType = SchedulingActionType.GET_LOAD_TYPES
    platform: SchedulingPlatform = SchedulingPlatform.MANHATTAN

    endDate: str
    filterType: Optional[str] = ""
    locationId: Optional[str] = ""
    startDate: str


class ManhattanGetOpenSlotsRequest(SchedulingBaseRequest):
    """Manhattan-specific GET open slots request."""

    action: SchedulingActionType = SchedulingActionType.GET_OPEN_SLOTS
    platform: SchedulingPlatform = SchedulingPlatform.MANHATTAN

    endDate: str
    facilityId: str
    facilityText: Optional[str] = ""
    appointmentType: str
    poId: str
    appointmentId: Optional[str] = ""
    startDate: str

    @model_validator(mode="after")
    def validate_dates(self):
        if not self.startDate and not self.endDate:
            raise ValueError("Both startDate and endDate must be provided")
        elif bool(self.startDate) != bool(self.endDate):
            raise ValueError(
                "Both startDate and endDate should be provided or neither"
            )

        try:
            start_date = datetime.strptime(self.startDate, "%Y-%m-%d")
            end_date = datetime.strptime(self.endDate, "%Y-%m-%d")
        except ValueError:
            raise ValueError(
                "startDate and endDate must be in format 'YYYY-MM-DD'"
            )

        if end_date < start_date:
            raise ValueError("endDate cannot be earlier than startDate")

        if start_date.date() < datetime.now().date():
            raise ValueError("startDate cannot be in the past")

        try:
            if self.appointmentType:
                AppointmentType(self.appointmentType)
        except ValueError:
            raise ValueError(
                f"Invalid appointmentType: '{self.appointmentType}'. Must be one of: {', '.join([t.value for t in AppointmentType])}"
            )

        if not self.poId:
            raise ValueError("poId is required")
        if not self.facilityId:
            raise ValueError("facilityId is required")
        if not self.appointmentType:
            raise ValueError("appointmentType is required")

        return self


class ManhattanGetWarehouseRequest(SchedulingBaseRequest):
    """Manhattan-specific GET warehouse request."""

    action: SchedulingActionType = SchedulingActionType.GET_WAREHOUSE
    platform: SchedulingPlatform = SchedulingPlatform.MANHATTAN

    endDate: str
    filterType: Optional[str] = ""
    locationId: Optional[str] = ""
    startDate: str


class ManhattanCancelAppointmentRequest(SchedulingBaseRequest):
    """Manhattan-specific cancel appointment request."""

    action: SchedulingActionType = SchedulingActionType.CANCEL_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.MANHATTAN

    appointmentId: str
    reason: Optional[str] = ""


class ManhattanGetAppointmentRequest(SchedulingBaseRequest):
    """Manhattan-specific get appointment request."""

    action: SchedulingActionType = SchedulingActionType.GET_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.MANHATTAN

    appointment: ManhattanAppointmentData

    @model_validator(mode="after")
    def validate_appointment(self):
        if not self.appointment:
            raise ValueError("appointments cannot be empty")
        if not self.appointment.poId:
            raise ValueError("poId is required in appointment data")
        if not self.appointment.appointmentId:
            raise ValueError("appointmentId is required in appointment data")
        return self


class ManhattanMakeAppointmentRequest(SchedulingBaseRequest):
    """Manhattan-specific make appointment request."""

    action: SchedulingActionType = SchedulingActionType.MAKE_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.MANHATTAN

    appointments: List[ManhattanAppointmentData]

    @model_validator(mode="after")
    def validate_appointment(self):
        if not self.appointments:
            raise ValueError("appointments cannot be empty")
        for appointment in self.appointments:
            if not appointment.poId:
                raise ValueError("poId is required in appointment data")
            if not appointment.appointmentTime:
                raise ValueError(
                    "appointmentTime is required in appointment data"
                )
            if not appointment.appointmentType:
                raise ValueError(
                    "appointmentType is required in appointment data"
                )
            if not appointment.facilityId:
                raise ValueError("facilityId is required in appointment data")
        return self


class ManhattanUpdateAppointmentRequest(SchedulingBaseRequest):
    """Manhattan-specific update appointment request."""

    action: SchedulingActionType = SchedulingActionType.UPDATE_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.MANHATTAN

    appointments: List[ManhattanAppointmentData]

    @model_validator(mode="after")
    def validate_appointments(self):
        if not self.appointments:
            raise ValueError("appointments cannot be empty")
        for appointment in self.appointments:
            if not appointment.poId:
                raise ValueError("poId is required in appointment data")
            if not appointment.appointmentId:
                raise ValueError(
                    "appointmentId is required in appointment data"
                )
            if not appointment.appointmentTime:
                raise ValueError(
                    "appointmentTime is required in appointment data"
                )
        return self


class ManhattanValidateAppointmentRequest(SchedulingBaseRequest):
    """Manhattan-specific validate appointment request."""

    action: SchedulingActionType = SchedulingActionType.VALIDATE_APPOINTMENT
    platform: SchedulingPlatform = SchedulingPlatform.MANHATTAN

    warehouse: ManhattanValidateAppointmentWarehouse


class ManhattanGetAppointmentResponse(BaseResponse):
    """Response after getting an appointment."""

    appointments: Optional[List[ManhattanAppointmentData]] = None
